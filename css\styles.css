* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>,
    "Helvetica Neue", <PERSON><PERSON>, sans-serif;
  line-height: 1.6;
  color: #333;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding: 20px;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  border-radius: 15px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

header {
  background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
  color: white;
  padding: 40px 30px;
  text-align: center;
}

header h1 {
  font-size: 2.5em;
  margin-bottom: 10px;
  font-weight: 600;
}

header p {
  font-size: 1.1em;
  opacity: 0.9;
}

main {
  padding: 40px 30px;
}

.input-section {
  margin-bottom: 30px;
}

.input-section label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #555;
}

.input-section input {
  width: 100%;
  padding: 15px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
  margin-bottom: 15px;
}

.input-section input:focus {
  outline: none;
  border-color: #4285f4;
  box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
}

button {
  background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
}

button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(66, 133, 244, 0.3);
}

button:active {
  transform: translateY(0);
}

button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.result-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  border: 1px solid #e9ecef;
}

.extension-info {
  margin-bottom: 25px;
}

.extension-info h3 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.3em;
}

.extension-info p {
  margin-bottom: 8px;
  word-break: break-all;
}

.extension-info strong {
  color: #555;
}

.download-section {
  text-align: center;
}

.download-btn {
  background: linear-gradient(135deg, #34a853 0%, #fbbc04 100%);
  font-size: 18px;
  padding: 18px 40px;
  margin-bottom: 10px;
}

.download-note {
  color: #666;
  font-size: 14px;
}

.error-section {
  background: #ffeaea;
  border: 1px solid #ffcdd2;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;
}

.error-section p {
  color: #d32f2f;
  font-weight: 500;
}

.info-section {
  background: #f0f7ff;
  border-radius: 12px;
  padding: 25px;
  border: 1px solid #cce7ff;
}

.info-section h3 {
  color: #1565c0;
  margin-bottom: 15px;
}

.info-section ol,
.info-section ul {
  margin-left: 20px;
  margin-bottom: 15px;
}

.info-section li {
  margin-bottom: 8px;
  color: #555;
}

.warning {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
}

.warning h4 {
  color: #856404;
  margin-bottom: 10px;
}

.warning ul {
  margin-left: 20px;
}

.warning li {
  color: #856404;
  margin-bottom: 5px;
}

.hidden {
  display: none;
}

@media (max-width: 768px) {
  body {
    padding: 10px;
  }

  header {
    padding: 30px 20px;
  }

  header h1 {
    font-size: 2em;
  }

  main {
    padding: 30px 20px;
  }

  .input-section input {
    font-size: 14px;
  }

  button {
    width: 100%;
    padding: 15px;
  }

  .download-btn {
    font-size: 16px;
    padding: 15px 30px;
  }
}