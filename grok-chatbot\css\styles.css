:root {
    --primary-color: #000000; /* Black */
    --primary-light: #333333; /* Dark Gray */
    --secondary-color: #ffffff; /* White */
    --text-color: #000000; /* Black */
    --background-color: #f0f0f0; /* Light Gray */
    --chat-background: #ffffff; /* White */
    --user-message-bg: #000000; /* Black */
    --user-message-text: #ffffff; /* White */
    --bot-message-bg: #e0e0e0; /* Lighter Gray */
    --bot-message-text: #000000; /* Black */
    --system-message-bg: #cccccc; /* Gray */
    --system-message-text: #000000; /* Black */
    --error-color: #d32f2f; /* Red for errors */
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.container {
    width: 100%;
    max-width: 1000px;
    height: 90vh;
    padding: 20px;
}

.chat-container {
    background-color: var(--chat-background);
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.chat-header {
    /* Use a solid black background instead of gradient */
    background: var(--primary-color);
    color: white;
    padding: 20px;
    text-align: center;
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.logo i {
    font-size: 24px;
}

.logo h1 {
    font-size: 24px;
    font-weight: 700;
}

.subtitle {
    font-size: 14px;
    opacity: 0.8;
    margin-top: 5px;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.message {
    display: flex;
    max-width: 80%;
}

.message.user {
    align-self: flex-end;
}

.message.bot {
    align-self: flex-start;
}

.message.system {
    align-self: center;
    max-width: 90%;
}

.message-content {
    padding: 12px 16px;
    border-radius: 18px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.user .message-content {
    background-color: var(--user-message-bg);
    color: var(--user-message-text);
    border-bottom-right-radius: 4px;
}

.bot .message-content {
    background-color: var(--bot-message-bg);
    color: var(--bot-message-text);
    border-bottom-left-radius: 4px;
}

.system .message-content {
    background-color: var(--system-message-bg);
    color: var(--system-message-text);
    text-align: center;
}

.message-content p {
    margin: 0 0 10px 0;
}

.message-content p:last-child {
    margin-bottom: 0;
}

.message-content pre {
    background-color: rgba(0, 0, 0, 0.05);
    padding: 10px;
    border-radius: 5px;
    overflow-x: auto;
    margin: 10px 0;
}

.message-content code {
    font-family: monospace;
    background-color: rgba(0, 0, 0, 0.05);
    padding: 2px 4px;
    border-radius: 3px;
}

.message-content pre code {
    background-color: transparent;
    padding: 0;
}

.message-content strong {
    font-weight: 700;
}

.message-content em {
    font-style: italic;
}

.message-content ul, .message-content ol {
    margin: 10px 0;
    padding-left: 20px;
}

.message-content blockquote {
    border-left: 3px solid var(--primary-color); /* Black border */
    padding-left: 10px;
    margin: 10px 0;
    color: #555;
}

.message-content a {
    color: var(--primary-light); /* Dark Gray for links */
    text-decoration: none;
}

.message-content a:hover {
    text-decoration: underline;
}

.message-content h1, .message-content h2, .message-content h3,
.message-content h4, .message-content h5, .message-content h6 {
    margin: 15px 0 10px 0;
    font-weight: 600;
}

.message-content h1 { font-size: 1.5em; }
.message-content h2 { font-size: 1.3em; }
.message-content h3 { font-size: 1.2em; }
.message-content h4 { font-size: 1.1em; }
.message-content h5 { font-size: 1em; }
.message-content h6 { font-size: 0.9em; }

.chat-input-container {
    display: flex;
    padding: 15px;
    background-color: white;
    border-top: 1px solid #eee;
}

#user-input {
    flex: 1;
    border: 1px solid #ddd;
    border-radius: 20px;
    padding: 12px 15px;
    font-size: 16px;
    resize: none;
    outline: none;
    transition: border-color 0.3s;
    font-family: 'Roboto', sans-serif;
}

#user-input:focus {
    border-color: var(--primary-color); /* Black border on focus */
}

.send-button {
    background-color: var(--primary-color); /* Black button */
    color: white;
    border: none;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    margin-left: 10px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: background-color 0.3s;
}

.send-button:hover {
    background-color: var(--primary-light); /* Dark Gray on hover */
}

.send-button i {
    font-size: 18px;
}

.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    padding: 10px;
}

.loading-dot {
    width: 8px;
    height: 8px;
    background-color: var(--primary-color); /* Black loading dots */
    border-radius: 50%;
    animation: bounce 1.5s infinite ease-in-out;
}

.loading-dot:nth-child(1) {
    animation-delay: 0s;
}

.loading-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.loading-dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes bounce {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

.error-message {
    color: var(--error-color);
    text-align: center;
    margin: 10px 0;
    font-size: 14px;
}

/* Add specific styles for reasoning tokens if needed */
.reasoning-token {
    color: #555; /* Example: Gray color for reasoning */
    font-style: italic;
    font-size: 0.9em;
    display: block; /* Display reasoning on a new line */
    margin-top: 5px;
    padding-left: 10px;
    border-left: 2px solid #ccc;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        padding: 10px;
        height: 100vh;
    }

    .message {
        max-width: 90%;
    }
}
