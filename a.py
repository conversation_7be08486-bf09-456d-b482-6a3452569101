import pygame
import math
import sys
import random

# --- Constants ---
SCREEN_WIDTH, SCREEN_HEIGHT = 800, 800
FPS = 60

# Colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
HEXAGON_COLOR = (40, 180, 220)
BALL_COLORS = [(255, 87, 34), (255, 193, 7), (139, 195, 74), (3, 169, 244), (156, 39, 176)]

# Physics Settings
GRAVITY = 0.5
BOUNCINESS = 0.8  # Coefficient of restitution for wall collisions
FRICTION = 0.01   # Friction factor for wall collisions
BALL_BOUNCINESS = 0.9 # Bounciness for ball-to-ball collisions

# --- Classes ---

class Ball:
    def __init__(self, pos, radius=10):
        self.pos = pygame.math.Vector2(pos)
        self.vel = pygame.math.Vector2(random.uniform(-2, 2), random.uniform(-2, 2))
        self.radius = radius
        self.color = random.choice(BALL_COLORS)
        self.mass = radius ** 2 # Mass proportional to area

    def update(self):
        # Apply gravity
        self.vel.y += GRAVITY
        # Update position
        self.pos += self.vel

    def draw(self, screen):
        pygame.draw.circle(screen, self.color, (int(self.pos.x), int(self.pos.y)), self.radius)

class Hexagon:
    def __init__(self, center, radius):
        self.center = pygame.math.Vector2(center)
        self.radius = radius
        self.angle = 0
        self.rotation_speed = 1.0  # degrees per frame
        self.rotation_direction = 1
        self.paused = False
        
    def update(self):
        if not self.paused:
            self.angle += self.rotation_speed * self.rotation_direction

    def get_vertices(self):
        """Calculates the current positions of the hexagon's 6 vertices."""
        points = []
        for i in range(6):
            angle_deg = 60 * i + self.angle
            angle_rad = math.radians(angle_deg)
            x = self.center.x + self.radius * math.cos(angle_rad)
            y = self.center.y + self.radius * math.sin(angle_rad)
            points.append(pygame.math.Vector2(x, y))
        return points

    def draw(self, screen):
        points = self.get_vertices()
        pygame.draw.polygon(screen, HEXAGON_COLOR, points, 4)

# --- Helper Functions ---

def draw_text(screen, text, pos, font, color=WHITE):
    """Renders and draws text on the screen."""
    text_surface = font.render(text, True, color)
    screen.blit(text_surface, pos)

def handle_wall_collisions(ball, hexagon):
    """Handles collision between a ball and the rotating hexagon walls."""
    vertices = hexagon.get_vertices()
    
    for i in range(6):
        p1 = vertices[i]
        p2 = vertices[(i + 1) % 6]
        
        # 1. Find the closest point on the line segment to the ball's center
        line_vec = p2 - p1
        p1_to_ball = ball.pos - p1
        t = p1_to_ball.dot(line_vec) / line_vec.length_squared()
        t = max(0, min(1, t)) # Clamp t to be on the segment
        closest_point = p1 + t * line_vec

        # 2. Check for collision
        dist_vec = ball.pos - closest_point
        dist_mag = dist_vec.length()
        
        if dist_mag < ball.radius:
            # Collision detected
            
            # 3. Correct position to prevent sinking
            overlap = ball.radius - dist_mag
            if dist_mag > 0:
                ball.pos += dist_vec.normalize() * overlap
            else: # Ball center is exactly on the line
                normal = (p2 - p1).rotate(90).normalize()
                ball.pos += normal * overlap

            # 4. Calculate collision response
            normal = dist_vec.normalize()
            
            # Calculate wall velocity at the point of impact
            # v = omega * r (tangential velocity)
            # omega is in rad/sec, rotation_speed is in deg/frame
            omega = math.radians(hexagon.rotation_speed * hexagon.rotation_direction)
            r_vec = closest_point - hexagon.center
            wall_velocity = pygame.math.Vector2(-r_vec.y, r_vec.x) * omega

            # Relative velocity of ball to wall
            relative_velocity = ball.vel - wall_velocity
            
            # Project relative velocity onto the normal
            vel_along_normal = relative_velocity.dot(normal)
            
            # If they are moving apart, do nothing
            if vel_along_normal > 0:
                continue

            # 5. Apply bounce (restitution)
            j = -(1 + BOUNCINESS) * vel_along_normal
            impulse = j * normal
            ball.vel += impulse
            
            # 6. Apply friction
            tangent = (ball.vel.dot(normal) * normal - ball.vel).normalize()
            friction_impulse = -FRICTION * tangent * j
            ball.vel += friction_impulse


def handle_ball_collisions(balls):
    """Handles collisions between all pairs of balls."""
    for i in range(len(balls)):
        for j in range(i + 1, len(balls)):
            ball1 = balls[i]
            ball2 = balls[j]
            
            dist_vec = ball2.pos - ball1.pos
            dist_mag = dist_vec.length()
            
            if dist_mag < ball1.radius + ball2.radius:
                # Collision detected
                
                # Normal and tangent vectors of collision
                if dist_mag > 0:
                    normal = dist_vec.normalize()
                else: # Balls are perfectly overlapped
                    normal = pygame.math.Vector2(1,0) # Avoid division by zero
                
                # 1. Correct position
                overlap = (ball1.radius + ball2.radius) - dist_mag
                correction = normal * overlap / 2
                ball1.pos -= correction
                ball2.pos += correction
                
                # 2. Elastic collision response
                # (See https://en.wikipedia.org/wiki/Elastic_collision#Two-dimensional_collision_with_two_moving_objects)
                v1 = ball1.vel
                v2 = ball2.vel
                x1 = ball1.pos
                x2 = ball2.pos
                m1 = ball1.mass
                m2 = ball2.mass
                
                term1 = (2 * m2 / (m1 + m2)) * (v1 - v2).dot(x1 - x2) / (x1 - x2).length_squared()
                term2 = (2 * m1 / (m1 + m2)) * (v2 - v1).dot(x2 - x1) / (x2 - x1).length_squared()
                
                ball1.vel -= term1 * (x1 - x2) * BALL_BOUNCINESS
                ball2.vel -= term2 * (x2 - x1) * BALL_BOUNCINESS


# --- Main Game Setup ---
def main():
    pygame.init()
    screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
    pygame.display.set_caption("Bouncing Balls in a Spinning Hexagon")
    clock = pygame.time.Clock()

    # Fonts for UI
    ui_font = pygame.font.Font(None, 28)
    info_font = pygame.font.Font(None, 24)

    # Game Objects
    hexagon = Hexagon((SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2), SCREEN_HEIGHT / 2 - 50)
    balls = []

    # --- Game Loop ---
    running = True
    while running:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            
            # --- Handle Controls ---
            if event.type == pygame.MOUSEBUTTONDOWN:
                if event.button == 1:  # Left click to add a ball
                    balls.append(Ball(event.pos, random.randint(10, 20)))
                elif event.button == 3: # Right click to remove a ball
                    # Find and remove the closest ball to the click
                    if balls:
                        mouse_pos = pygame.math.Vector2(event.pos)
                        closest_ball = min(balls, key=lambda b: (b.pos - mouse_pos).length())
                        if (closest_ball.pos - mouse_pos).length() < closest_ball.radius:
                            balls.remove(closest_ball)
                            
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_UP:
                    hexagon.rotation_speed += 0.2
                if event.key == pygame.K_DOWN:
                    hexagon.rotation_speed = max(0, hexagon.rotation_speed - 0.2)
                if event.key == pygame.K_r:
                    hexagon.rotation_direction *= -1
                if event.key == pygame.K_SPACE:
                    hexagon.paused = not hexagon.paused

        # --- Update ---
        hexagon.update()
        for ball in balls:
            ball.update()
            handle_wall_collisions(ball, hexagon)
        handle_ball_collisions(balls)

        # --- Draw ---
        screen.fill(BLACK)
        
        # Draw game objects
        hexagon.draw(screen)
        for ball in balls:
            ball.draw(screen)

        # Draw UI
        # Controls info (top-left)
        controls_text = [
            "Controls:",
            "- Click: Add a ball",
            "- Right-click: Remove a ball",
            "- Up/Down: Adjust rotation speed",
            "- R: Reverse rotation",
            "- Space: Pause rotation"
        ]
        for i, line in enumerate(controls_text):
            draw_text(screen, line, (10, 10 + i * 25), ui_font)

        # Simulation settings info (bottom-left)
        rotation_dir_str = "CW" if hexagon.rotation_direction > 0 else "CCW"
        if hexagon.paused: rotation_dir_str = "Paused"
            
        settings_text = [
            "Simulation Settings:",
            f"- Rotation Speed: {hexagon.rotation_speed:.1f} deg/frame ({rotation_dir_str})",
            f"- Ball Count: {len(balls)}"
        ]
        for i, line in enumerate(settings_text):
            draw_text(screen, line, (10, SCREEN_HEIGHT - 80 + i * 25), info_font)
            
        pygame.display.flip()
        clock.tick(FPS)

    pygame.quit()
    sys.exit()

if __name__ == '__main__':
    main()