document.addEventListener('DOMContentLoaded', () => {
    // Configure marked.js
    marked.setOptions({
        breaks: true,        // Add <br> on single line breaks
        gfm: true,          // Use GitHub Flavored Markdown
        headerIds: false,   // Don't add IDs to headers
        mangle: false,      // Don't mangle email addresses
        sanitize: false,    // Don't sanitize HTML (we'll handle this separately)
    });

    // DOM Elements
    const chatMessages = document.getElementById('chat-messages');
    const userInput = document.getElementById('user-input');
    const sendButton = document.getElementById('send-button');

    // --- Requesty API Configuration ---
    // IMPORTANT: Replace 'YOUR_REQUESTY_API_KEY' with your actual key.
    // Consider using environment variables or a secure configuration method in a real application.
    const REQUESTY_API_KEY = 'sk-sL9zPhCmQn2DIHNJXubaMj81HXjMSv5LrQ7QfQb3l+w/GbThAI2pepXYQv6EhYhsBxQdrT9Io2CSSYSlHrJHSgVGCQgPBpKPAqpA3R74Xgs=';
    const BASE_URL = 'https://router.requesty.ai/v1';
    const MODEL_NAME = 'xai/grok-3-mini-beta:high';
    // --- End Requesty API Configuration ---

    // Auto-resize textarea as user types
    userInput.addEventListener('input', () => {
        userInput.style.height = 'auto';
        userInput.style.height = (userInput.scrollHeight) + 'px';
    });

    // Send message when Enter key is pressed (without Shift)
    userInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });

    // Send message when send button is clicked
    sendButton.addEventListener('click', sendMessage);

    // Function to send message
    async function sendMessage() {
        const message = userInput.value.trim();

        // Don't send empty messages
        if (!message) return;

        // Check if API key is set
        if (REQUESTY_API_KEY === 'YOUR_REQUESTY_API_KEY') {
            addErrorMessage('Please set your Requesty API key in js/app.js');
            return;
        }

        // Add user message to chat
        addMessageToChat('user', message);

        // Clear input field and reset height
        userInput.value = '';
        userInput.style.height = 'auto';

        // Create a bot message container for streaming
        const botMessageDiv = document.createElement('div');
        botMessageDiv.classList.add('message', 'bot');

        const messageContent = document.createElement('div');
        messageContent.classList.add('message-content');

        const messageParagraph = document.createElement('p'); // Main content paragraph
        const reasoningSpan = document.createElement('span'); // Span for reasoning tokens
        reasoningSpan.classList.add('reasoning-token'); // Add class for styling

        messageContent.appendChild(messageParagraph);
        messageContent.appendChild(reasoningSpan); // Append reasoning span
        botMessageDiv.appendChild(messageContent);
        chatMessages.appendChild(botMessageDiv);

        // Scroll to the new message
        scrollToBottom();

        try {
            // Call the API with streaming
            await streamGrokAPI(message, messageParagraph, reasoningSpan); // Pass reasoningSpan

        } catch (error) {
            // Remove the bot message if there was an error
            botMessageDiv.remove();

            // Show error message
            const errorMessage = error.message || 'An error occurred while processing your request.';
            addErrorMessage(errorMessage);
            console.error('Error:', error);
        }

        // Scroll to bottom of chat again after completion
        scrollToBottom();
    }

    // Function to stream responses from the Requesty API (Grok)
    async function streamGrokAPI(userMessage, targetContentElement, targetReasoningElement) {
        try {
            // Get previous messages to maintain context (limited to last 10 for simplicity)
            const messageElements = chatMessages.querySelectorAll('.message:not(.system)');
            const messages = [
                { role: "system", content: "You are a helpful reasoning assistant." } // Updated system prompt
            ];

            // Add previous messages for context (up to last 10 messages)
            const maxPreviousMessages = 10;
            const startIndex = Math.max(0, messageElements.length - maxPreviousMessages);

            for (let i = startIndex; i < messageElements.length - 1; i++) { // -1 to exclude the current placeholder
                const element = messageElements[i];
                const role = element.classList.contains('user') ? 'user' : 'assistant';
                const pElement = element.querySelector('p'); // Assuming main content is in <p>

                if (!pElement) continue; // Skip if no content paragraph

                const content = pElement.textContent; // Get text content only for history
                if (content) { // Only add if there's content
                    messages.push({ role, content });
                }
            }

            // Add current user message
            messages.push({ role: "user", content: userMessage });

            // API request with streaming and reasoning enabled
            const response = await fetch(`${BASE_URL}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${REQUESTY_API_KEY}` // Use Requesty API Key
                },
                body: JSON.stringify({
                    model: MODEL_NAME, // Use Grok model
                    messages: messages,
                    stream: true, // Enable streaming
                    include_reasoning: true // Enable reasoning tokens
                    // temperature: 0.1, // Optional: Keep or remove based on preference/support
                    // top_p: 0.1        // Optional: Keep or remove based on preference/support
                })
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                const errorMsg = errorData.error?.message || `API error: ${response.status}`;
                // Check for specific API key error
                if (response.status === 401) {
                     throw new Error('Authentication failed. Please check your Requesty API key in js/app.js.');
                }
                throw new Error(errorMsg);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder("utf-8");
            let fullContentText = "";
            let fullReasoningText = "";

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                // Decode the chunk
                const chunk = decoder.decode(value, { stream: true });

                // Process the chunk (which may contain multiple events)
                const lines = chunk.split('\n').filter(line => line.trim() !== '');

                for (const line of lines) {
                    // Skip empty lines and [DONE] messages
                    if (line === 'data: [DONE]') continue;

                    // Remove the 'data: ' prefix
                    const jsonStr = line.replace(/^data: /, '');

                    try {
                        const json = JSON.parse(jsonStr);
                        let contentDelta = '';
                        let reasoningDelta = '';

                        if (json.choices && json.choices[0] && json.choices[0].delta) {
                            // Check for content
                            if (json.choices[0].delta.content) {
                                contentDelta = json.choices[0].delta.content;
                                fullContentText += contentDelta;
                                // Update the main content with parsed markdown
                                targetContentElement.innerHTML = marked.parse(fullContentText);
                            }
                            // Check for reasoning tokens
                            if (json.choices[0].delta.reasoning) {
                                reasoningDelta = json.choices[0].delta.reasoning;
                                fullReasoningText += reasoningDelta;
                                // Update the reasoning element (no markdown parsing needed for reasoning)
                                // Escape HTML to prevent injection if reasoning contains HTML-like chars
                                const escapedReasoning = fullReasoningText
                                    .replace(/&/g, '&amp;')
                                    .replace(/</g, '&lt;')
                                    .replace(/>/g, '&gt;');
                                targetReasoningElement.textContent = escapedReasoning;
                            }

                            // Scroll to bottom as content/reasoning is added
                            if (contentDelta || reasoningDelta) {
                                scrollToBottom();
                            }
                        }
                    } catch (e) {
                        console.warn('Error parsing JSON from stream:', e, jsonStr);
                        // Don't stop the stream for a single bad chunk if possible
                    }
                }
            }

            // Final update with complete text (redundant if updates happen in loop, but safe)
            targetContentElement.innerHTML = marked.parse(fullContentText);
            const finalEscapedReasoning = fullReasoningText
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;');
            targetReasoningElement.textContent = finalEscapedReasoning;

        } catch (error) {
            console.error('API streaming error:', error);
            // Provide a more specific error message if possible
            throw new Error(error.message || 'Failed to communicate with the Requesty API. Please try again later.');
        }
    }

    // Function to add a message to the chat
    function addMessageToChat(sender, content) {
        const messageDiv = document.createElement('div');
        messageDiv.classList.add('message', sender);

        const messageContent = document.createElement('div');
        messageContent.classList.add('message-content');

        // For user messages, escape HTML and handle line breaks
        if (sender === 'user') {
            const escapedContent = content
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#039;')
                .replace(/\n/g, '<br>'); // Handle newlines

            messageContent.innerHTML = `<p>${escapedContent}</p>`;
        } else {
             // For bot messages, create placeholder elements (content added during streaming)
             // We create these elements in sendMessage now, so just append the container
        }

        messageDiv.appendChild(messageContent); // Append even if bot (will be filled later)
        chatMessages.appendChild(messageDiv);

        scrollToBottom();
    }

    // Function to add error message
    function addErrorMessage(message) {
        const errorDiv = document.createElement('div');
        errorDiv.classList.add('error-message');
        errorDiv.textContent = message;
        chatMessages.appendChild(errorDiv);
        scrollToBottom(); // Scroll to show error
    }

    // Function to scroll to bottom of chat
    function scrollToBottom() {
        // Add a small delay to allow the DOM to update, especially during streaming
        setTimeout(() => {
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }, 50);
    }

    // Remove the loading indicator function as streaming provides immediate feedback
    // function showLoadingIndicator() { ... }
});
