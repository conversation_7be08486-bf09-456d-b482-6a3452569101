// JavaScript version of the C factorial calculation
function calculateFactorial100() {
    let bigInt = new Array(158).fill(0);
    bigInt[0] = 1; // Initialize to 1
    let length = 1; // Tracks number of digits, starts at 1

    // Multiply from 2 to 100
    for (let i = 2; i <= 100; i++) {
        let carry = 0; // Carry for multiplication
        for (let j = 0; j < length; j++) {
            let temp = bigInt[j] * i + carry; // Temp result of digit * i + carry
            bigInt[j] = temp % 10;            // Update current digit
            carry = Math.floor(temp / 10);    // Carry to next digit
        }
        while (carry > 0) {                   // Append remaining carry digits
            bigInt[length] = carry % 10;
            carry = Math.floor(carry / 10);
            length++;
        }
    }

    // Print first three digits (most significant)
    console.log(`First 3 digits of 100!: ${bigInt[length-1]}${bigInt[length-2]}${bigInt[length-3]}`);
    
    // Print the full number
    let fullNumber = '';
    for (let i = length - 1; i >= 0; i--) {
        fullNumber += bigInt[i];
    }
    console.log(`100! = ${fullNumber}`);
    console.log(`Total digits: ${length}`);
}

calculateFactorial100();
