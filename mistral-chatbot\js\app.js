document.addEventListener('DOMContentLoaded', () => {
    // Configure marked.js
    marked.setOptions({
        breaks: true,        // Add <br> on single line breaks
        gfm: true,          // Use GitHub Flavored Markdown
        headerIds: false,   // Don't add IDs to headers
        mangle: false,      // Don't mangle email addresses
        sanitize: false,    // Don't sanitize HTML (we'll handle this separately)
    });

    // DOM Elements
    const chatMessages = document.getElementById('chat-messages');
    const userInput = document.getElementById('user-input');
    const sendButton = document.getElementById('send-button');

    // API Key (in a real application, this should be secured on the server side)
    const API_KEY = 'ACm05Y5Fkg3sp8TPHQ8Yux96h1RmXCmc';
    const MODEL = 'mistral-large-latest';

    // Auto-resize textarea as user types
    userInput.addEventListener('input', () => {
        userInput.style.height = 'auto';
        userInput.style.height = (userInput.scrollHeight) + 'px';
    });

    // Send message when Enter key is pressed (without Shift)
    userInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });

    // Send message when send button is clicked
    sendButton.addEventListener('click', sendMessage);

    // Function to send message
    async function sendMessage() {
        const message = userInput.value.trim();

        // Don't send empty messages
        if (!message) return;

        // Add user message to chat
        addMessageToChat('user', message);

        // Clear input field and reset height
        userInput.value = '';
        userInput.style.height = 'auto';

        // Create a bot message container for streaming
        const botMessageDiv = document.createElement('div');
        botMessageDiv.classList.add('message', 'bot');

        const messageContent = document.createElement('div');
        messageContent.classList.add('message-content');

        const messageParagraph = document.createElement('p');
        messageContent.appendChild(messageParagraph);
        botMessageDiv.appendChild(messageContent);
        chatMessages.appendChild(botMessageDiv);

        // Scroll to the new message
        scrollToBottom();

        try {
            // Call the API with streaming
            await streamMistralAPI(message, messageParagraph);

        } catch (error) {
            // Remove the bot message if there was an error
            botMessageDiv.remove();

            // Show error message
            const errorMessage = error.message || 'An error occurred while processing your request.';
            addErrorMessage(errorMessage);
            console.error('Error:', error);
        }

        // Scroll to bottom of chat again after completion
        scrollToBottom();
    }

    // Function to stream responses from the Mistral API
    async function streamMistralAPI(userMessage, targetElement) {
        try {
            // Get previous messages to maintain context (limited to last 10 for simplicity)
            const messageElements = chatMessages.querySelectorAll('.message:not(.system)');
            const messages = [
                { role: "system", content: "You are a helpful assistant" }
            ];

            // Add previous messages for context (up to last 10 messages)
            const maxPreviousMessages = 10;
            const startIndex = Math.max(0, messageElements.length - maxPreviousMessages);

            for (let i = startIndex; i < messageElements.length - 1; i++) { // -1 to exclude the current message
                const element = messageElements[i];
                // Skip loading messages or messages without content
                if (element.classList.contains('loading')) continue;

                const role = element.classList.contains('user') ? 'user' : 'assistant';
                const pElement = element.querySelector('p');

                // Skip if p element doesn't exist
                if (!pElement) continue;

                const content = pElement.textContent;
                messages.push({ role, content });
            }

            // Add current user message
            messages.push({ role: "user", content: userMessage });

            // API request with streaming
            const response = await fetch('https://api.mistral.ai/v1/chat/completions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${API_KEY}`
                },
                body: JSON.stringify({
                    model: MODEL,
                    messages: messages,
                    temperature: 0.7,
                    top_p: 1,
                    stream: true // Enable streaming
                })
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.error?.message || `API error: ${response.status}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder("utf-8");
            let fullText = "";

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                // Decode the chunk
                const chunk = decoder.decode(value, { stream: true });

                // Process the chunk (which may contain multiple events)
                const lines = chunk.split('\n').filter(line => line.trim() !== '');

                for (const line of lines) {
                    // Skip empty lines and [DONE] messages
                    if (line === 'data: [DONE]') continue;

                    // Remove the 'data: ' prefix
                    const jsonStr = line.replace(/^data: /, '');

                    try {
                        const json = JSON.parse(jsonStr);
                        if (json.choices && json.choices[0] && json.choices[0].delta && json.choices[0].delta.content) {
                            const content = json.choices[0].delta.content;
                            fullText += content;

                            // Update the message with the current accumulated text
                            // Use marked.js to render markdown
                            targetElement.innerHTML = marked.parse(fullText);

                            // Scroll to bottom as content is added
                            scrollToBottom();
                        }
                    } catch (e) {
                        console.error('Error parsing JSON from stream:', e, jsonStr);
                    }
                }
            }

            // Final update with complete text
            targetElement.innerHTML = marked.parse(fullText);
            return fullText;
        } catch (error) {
            console.error('API streaming error:', error);
            throw new Error('Failed to communicate with the Mistral API. Please try again later.');
        }
    }

    // Function to add a message to the chat
    function addMessageToChat(sender, content) {
        const messageDiv = document.createElement('div');
        messageDiv.classList.add('message', sender);

        const messageContent = document.createElement('div');
        messageContent.classList.add('message-content');

        // Use marked.js to parse markdown
        if (sender === 'user') {
            // For user messages, we'll just escape HTML and handle line breaks
            const escapedContent = content
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#039;')
                .replace(/\n/g, '<br>');

            messageContent.innerHTML = `<p>${escapedContent}</p>`;
        } else {
            // For bot messages, parse markdown
            messageContent.innerHTML = marked.parse(content);
        }

        messageDiv.appendChild(messageContent);
        chatMessages.appendChild(messageDiv);

        scrollToBottom();
    }

    // Function to add error message
    function addErrorMessage(message) {
        const errorDiv = document.createElement('div');
        errorDiv.classList.add('error-message');
        errorDiv.textContent = message;
        chatMessages.appendChild(errorDiv);
    }

    // Function to scroll to bottom of chat
    function scrollToBottom() {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
});
