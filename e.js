// npm install heap-js
const { MinHeap } = require('heap-js');

/**
 * Draw an exponential RV with mean `mean`.
 */
function expRV(mean) {
  return -mean * Math.log(Math.random());
}

/**
 * Draw a Uniform[a,b] RV.
 */
function uniformRV(a, b) {
  return a + (b - a) * Math.random();
}

/**
 * Simulate one 8‐hour day and return the number of unloaded trucks.
 */
function simulateOneDay() {
  const T = 480;            // horizon in minutes
  const meanIA = 7;         // mean interarrival (min)
  const numServers = 3;     // number of docks

  let now = 0;              // current time
  let busy = 0;             // busy servers
  let queue = 0;            // number in yard
  let departures = 0;       // completed unloads

  // Event types: 'arrival' or 'departure'
  // We use a min‐heap ordered by event.time
  const heap = new MinHeap((a, b) => a.time - b.time);

  // Schedule first arrival
  heap.push({ time: expRV(meanIA), type: 'arrival' });

  while (!heap.isEmpty()) {
    const evt = heap.pop();
    now = evt.time;
    if (now > T) break;

    if (evt.type === 'arrival') {
      // schedule next arrival
      const tNext = now + expRV(meanIA);
      if (tNext <= T) heap.push({ time: tNext, type: 'arrival' });

      // arrival joins yard or goes straight to service
      if (busy < numServers) {
        busy++;
        // service time = 1 + U[20,35]
        const s = 1 + uniformRV(20, 35);
        heap.push({ time: now + s, type: 'departure' });
      } else {
        queue++;
      }
    } else {
      // departure
      departures++;
      busy--;
      if (queue > 0) {
        queue--;
        busy++;
        const s = 1 + uniformRV(20, 35);
        heap.push({ time: now + s, type: 'departure' });
      }
    }
  }

  return departures;
}

// Example: one run
console.log('Trucks unloaded in one day:', simulateOneDay());

// (Optionally) run many days to get an average
const runs = 1000;
let sum = 0;
for (let i = 0; i < runs; i++) {
  sum += simulateOneDay();
}
console.log(`Average over ${runs} days:`, (sum / runs).toFixed(2));