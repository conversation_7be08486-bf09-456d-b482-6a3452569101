class ChromeExtensionDownloader {
  constructor() {
    this.initializeElements();
    this.attachEventListeners();
  }

  initializeElements() {
    this.extensionUrlInput = document.getElementById("extensionUrl");
    this.generateBtn = document.getElementById("generateBtn");
    this.downloadBtn = document.getElementById("downloadBtn");
    this.resultSection = document.getElementById("result");
    this.errorSection = document.getElementById("error");
    this.extensionIdSpan = document.getElementById("extensionId");
    this.storeUrlSpan = document.getElementById("storeUrl");
    this.errorMessage = document.getElementById("errorMessage");
  }

  attachEventListeners() {
    this.generateBtn.addEventListener("click", () => this.handleGenerate());
    this.downloadBtn.addEventListener("click", () => this.handleDownload());
    this.extensionUrlInput.addEventListener("keypress", (e) => {
      if (e.key === "Enter") {
        this.handleGenerate();
      }
    });
    this.extensionUrlInput.addEventListener("input", () => {
      this.hideMessages();
    });
  }

  extractExtensionId(url) {
    // Handle different Chrome Web Store URL formats
    const patterns = [
      // New format: https://chromewebstore.google.com/detail/extension-name/extension-id
      /chromewebstore\.google\.com\/detail\/[^\/]+\/([a-z]{32})/i,
      // Old format: https://chrome.google.com/webstore/detail/extension-name/extension-id
      /chrome\.google\.com\/webstore\/detail\/[^\/]+\/([a-z]{32})/i,
      // Direct ID format: https://chromewebstore.google.com/detail/extension-id
      /chromewebstore\.google\.com\/detail\/([a-z]{32})/i,
      // URL with just the ID at the end
      /\/([a-z]{32})(?:[\/\?#].*)?$/i,
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match && match[1]) {
        return match[1];
      }
    }

    return null;
  }

  validateChromeStoreUrl(url) {
    const validDomains = [
      "chromewebstore.google.com",
      "chrome.google.com/webstore",
    ];

    try {
      const urlObj = new URL(url);
      return validDomains.some((domain) => urlObj.href.includes(domain));
    } catch {
      return false;
    }
  }

  generateCrxUrl(extensionId) {
    // Chrome's CRX download URL format
    const baseUrl = "https://clients2.google.com/service/update2/crx";
    const params = new URLSearchParams({
      response: "redirect",
      prodversion: "98.0.4758.102",
      acceptformat: "crx2,crx3",
      x: `id%3D${extensionId}%26uc`,
    });

    return `${baseUrl}?${params.toString()}`;
  }

  showError(message) {
    this.errorMessage.textContent = message;
    this.errorSection.classList.remove("hidden");
    this.resultSection.classList.add("hidden");
  }

  hideMessages() {
    this.errorSection.classList.add("hidden");
    this.resultSection.classList.add("hidden");
  }

  showResult(extensionId, originalUrl) {
    this.extensionIdSpan.textContent = extensionId;
    this.storeUrlSpan.textContent = originalUrl;
    this.resultSection.classList.remove("hidden");
    this.errorSection.classList.add("hidden");

    // Store the extension ID for download
    this.currentExtensionId = extensionId;
  }

  async handleGenerate() {
    const url = this.extensionUrlInput.value.trim();

    if (!url) {
      this.showError("Please enter a Chrome Web Store URL");
      return;
    }

    if (!this.validateChromeStoreUrl(url)) {
      this.showError(
        "Please enter a valid Chrome Web Store URL (chromewebstore.google.com or chrome.google.com/webstore)"
      );
      return;
    }

    const extensionId = this.extractExtensionId(url);

    if (!extensionId) {
      this.showError(
        "Could not extract extension ID from URL. Please make sure the URL is correct."
      );
      return;
    }

    if (extensionId.length !== 32) {
      this.showError(
        "Invalid extension ID format. Extension IDs should be 32 characters long."
      );
      return;
    }

    this.showResult(extensionId, url);
  }

  async handleDownload() {
    if (!this.currentExtensionId) {
      this.showError("No extension ID available. Please generate first.");
      return;
    }

    try {
      const crxUrl = this.generateCrxUrl(this.currentExtensionId);

      // Create a temporary link to trigger download
      const link = document.createElement("a");
      link.href = crxUrl;
      link.download = `${this.currentExtensionId}.crx`;
      link.target = "_blank";

      // Append to body, click, and remove
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Show success message
      this.showTemporaryMessage("Download started! Check your downloads folder.");
    } catch (error) {
      console.error("Download error:", error);
      this.showError(
        "Failed to start download. The extension might not be available or there could be a network issue."
      );
    }
  }

  showTemporaryMessage(message) {
    const messageDiv = document.createElement("div");
    messageDiv.className = "success-message";
    messageDiv.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4caf50;
      color: white;
      padding: 15px 20px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 1000;
      font-weight: 500;
    `;
    messageDiv.textContent = message;

    document.body.appendChild(messageDiv);

    // Remove after 3 seconds
    setTimeout(() => {
      document.body.removeChild(messageDiv);
    }, 3000);
  }
}

// Initialize the app when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  new ChromeExtensionDownloader();
});

// Add example URL on page load for demonstration
window.addEventListener("load", () => {
  const input = document.getElementById("extensionUrl");
  input.placeholder =
    "https://chromewebstore.google.com/detail/google-translate/aapbdbdomjkkjkaonfhkkikfgjllcleb";
});