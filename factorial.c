#include <stdio.h>

int main() {
    unsigned char bigInt[158] = {1}; // Array to store 100!, 158 digits, initialized to 1
    unsigned char length = 1;        // Tracks number of digits, starts at 1

    // Multiply from 2 to 100
    for (unsigned char i = 2; i <= 100; i++) {
        unsigned char carry = 0;     // Carry for multiplication, max 99
        for (unsigned char j = 0; j < length; j++) {
            int temp = bigInt[j] * i + carry; // Temp result of digit * i + carry
            bigInt[j] = temp % 10;            // Update current digit
            carry = temp / 10;                // Carry to next digit
        }
        while (carry > 0) {                   // Append remaining carry digits
            bigInt[length] = carry % 10;
            carry = carry / 10;
            length++;
        }
    }

    // Print first three digits (most significant)
    printf("First 3 digits of 100!: %d%d%d\n", bigInt[length-1], bigInt[length-2], bigInt[length-3]);
    
    // Optional: Print the full number
    printf("100! = ");
    for (int i = length - 1; i >= 0; i--) {
        printf("%d", bigInt[i]);
    }
    printf("\n");

    return 0;
}
