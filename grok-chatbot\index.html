<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grok Chatbot</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
</head>
<body>
    <div class="container">
        <div class="chat-container">
            <div class="chat-header">
                <div class="logo">
                    <i class="fas fa-brain"></i>
                    <h1><PERSON>rok Chatbot</h1>
                </div>
                <div class="subtitle">Powered by Requesty</div>
            </div>
            <div class="chat-messages" id="chat-messages">
                <div class="message system">
                    <div class="message-content">
                        <p>Hello! I'm Grok, your reasoning AI assistant. How can I help you today?</p>
                    </div>
                </div>
                <!-- Messages will be added here dynamically -->
            </div>
            <div class="chat-input-container">
                <textarea id="user-input" placeholder="Type your message here..." rows="1"></textarea>
                <button id="send-button" class="send-button">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>
    <script src="js/app.js"></script>
</body>
</html>
